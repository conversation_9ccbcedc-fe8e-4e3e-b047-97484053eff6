@echo off
chcp 65001 >nul
echo QMT启动器 Power by 禄得网(https://lude.cc)
echo ------------------------------
:: 获取当前批处理文件相关变量
set FULL_PATH=%~0
set FILE_NAME=%~nx0
set BASE_PATH=%~dp0

:: 判断是否允许运行
echo %BASE_PATH% | findstr /C:"lude_utils" >nul
if not %errorlevel% equ 0 (
    echo 非法路径!
    pause
    exit
)

:: 切换路径
cd /d %~dp0

:: 获取当前日期并转换为 YYYY-MM-DD 格式
for /f "tokens=2 delims==." %%A in ('wmic os get localdatetime /value') do set "datetime=%%A"
set "YYYY=%datetime:~0,4%"
set "MM=%datetime:~4,2%"
set "DD=%datetime:~6,2%"
set "today=%YYYY%-%MM%-%DD%"
echo 当前日期为: %today%

:: 初始化变量 date
set "date=0"
:: 判断.run_date文件是否存在
if exist "%BASE_PATH%.run_date" (
    :: 读取文件第一行内容并赋值给变量 date
	for /f "delims=" %%a in (%BASE_PATH%.run_date) do (
		set "date=%%a"
	)
)
echo %date%
:: 通过 tasklist 检查 XtItClient.exe 是否存在
tasklist /FI "IMAGENAME eq XtItClient.exe" | find /i "XtItClient.exe" >nul

:: 根据查找结果判断
if %ERRORLEVEL%==0 (
	if "%today%"=="%date%" (
		echo qmt程序已正常运行，直接退出！
		pause
		exit
	)
)


:: 若不存在.login文件则准备抓取
if not exist "%BASE_PATH%.login" (
    echo "请手动打开QMT并登入，程序将自动收集登入所需信息。（勿关闭本窗口）"
    :loop_linkfile
    if exist "..\linkMini" (
        for /f "tokens=1,2 delims==" %%a in ('findstr /i "^actpwd=" "..\linkMini"') do (
            echo actpwd=%%b >> "%BASE_PATH%.login"
        )
		for /f "tokens=1,2 delims==" %%a in ('findstr /i "^address=" "..\linkMini"') do (
            echo address=%%b >> "%BASE_PATH%.login"
        )
		for /f "tokens=1,2 delims==" %%a in ('findstr /i "^pwd=" "..\linkMini"') do (
            echo pwd=%%b >> "%BASE_PATH%.login"
        )
		for /f "tokens=1,2 delims==" %%a in ('findstr /i "^user=" "..\linkMini"') do (
            echo user=%%b  >> "%BASE_PATH%.login"
        )
        echo 登入信息已收集完毕
		goto create_task
    ) else (
        goto loop_linkfile
    )
	:: 计划任务
	:create_task
	echo 添加QMT启动任务计划
	schtasks /create /f /tn "qmt_starter_1" /tr "%FULL_PATH%" /sc weekly /d MON,TUE,WED,THU,FRI /st 08:50
	schtasks /create /f /tn "qmt_starter_2" /tr "%FULL_PATH%" /sc weekly /d MON,TUE,WED,THU,FRI /st 08:55
	schtasks /create /f /tn "qmt_starter_3" /tr "%FULL_PATH%" /sc weekly /d MON,TUE,WED,THU,FRI /st 09:00
	echo ==================================================
	echo 配置完成，请重新运行本程序以检查能否自动登入
	echo 如需重新配置，请删除lude_utils文件夹下的.login文件，并重新运行本程序
	pause
	exit
) else (
	:: 尝试关闭现有的qmt进程
	echo 关闭现有的qmt进程...
	taskkill /IM XtItClient.exe /F
	(
	echo [proxy]
	echo proxyip=
	echo proxyname=
	echo proxyneedcheck=0
	echo proxypassword=
	echo proxyport=80
	echo proxytype=0
	echo [local]
	echo bgmssl=0
	echo client=mini
	echo clienttype=0
	echo starttype=0
	) > %~dp0../linkQmt
	REM 逐行.login并追加到linkQmt
	for /f "delims=" %%a in (%BASE_PATH%.login) do (
		echo %%a >> %~dp0../linkQmt
	)
	cd %~dp0../ && start "" "XtItClient.exe" "linkQmt"
)