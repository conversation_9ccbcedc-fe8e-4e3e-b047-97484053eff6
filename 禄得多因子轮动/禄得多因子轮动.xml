<?xml version="1.0" encoding="utf-8"?>
<TCStageLayout>
    <control note="控件">
        <variable note="控件">
            <item position="" bind="ADJ_TIME_LIST_STR" value="14:50:00" note="格式为:HH:MM:SS，多个时间用,隔开，例:11:25:00,14:50:00" name="换仓时间" type="intput"/>
            <item position="" bind="BALANCE_LIMIT" value="1000000" note="策略最大资金容量" name="资金容量(元)" type="intput"/>
            <item position="" bind="LUDE_CONF_STR" value="" note="将禄得网配置字符串粘贴在此处" name="禄得配置" type="intput"/>
            <item position="" bind="EXEC_STR" value="" note="执行自定义代码，通常用来生成自定义因子" name="自定义代码" type="intput"/>
            <item position="" bind="IPO_STR" value="True" note="若勾选会对所有新发转债进行申购" name="自动打新债" type="checkBox"/>
            <item position="" bind="ORR_STR" value="True" note="若勾选会在收盘后将剩余资金进行逆回购" name="自动逆回购" type="checkBox"/>
            <item position="" bind="ORDER_TIME_PROTECTION_STR" value="True" note="若当前时间已经过了设置的挂单时间，且在15:00前，直接运行止盈止损功能" name="挂单时间保护" type="checkBox"/>
            <item position="" bind="ORDER_CALLBACK_STR" value="False" note="订单信息" name="订单信息" type="checkBox"/>
            <item position="" bind="DEAL_CALLBACK_STR" value="False" note="成交信息" name="成交信息" type="checkBox"/>
            <item position="" bind="ORDERERROR_CALLBACK_STR" value="True" note="订单错误信息" name="订单错误信息" type="checkBox"/>
            <item position="" bind="IGNORE_LIST_STR" value="" note="用英文半角逗号分割，例：123015.SZ,110044.SH" name="忽略标的列表" type="intput"/>
            <item position="" bind="PUSHPLUS_TOKEN" value="" note="不填则不发送pushplus消息" name="PUSHPLUS TOKEN" type="intput"/>
            <item position="" bind="BARK_TOKEN" value="" note="不填则不发送bark消息" name="BARK TOKEN" type="intput"/>
            <item position="" bind="WECHAT_TOKEN" value="" note="不填则不发送企业微信消息" name="WECHAT_TOKEN" type="intput"/>
            <item position="" bind="HEARTBEAT_INTERVAL" value="0" note="qmt运行状态检测时间间隔（分钟）" name="状态检测间隔" type="intput"/>
            <item comboType="custom" position="" list="DEBUG,INFO,WARNING,ERROR" bind="LOG_LEVEL_STR" value="INFO" note="默认INFO，无需改变" name="日志等级" type="combo"/>
        </variable>
    </control>
</TCStageLayout>
